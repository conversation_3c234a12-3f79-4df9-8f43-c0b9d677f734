document.addEventListener('DOMContentLoaded', function () {
    // --- Handle Filter Form Submission ---
    const filterForm = document.getElementById('filter-form');
    if (filterForm) {
        filterForm.addEventListener('submit', function (event) {
            // Get all filter values
            const contenedor   = document.getElementById('contenedor').value;
            const activoTexto  = document.getElementById('activo_texto').value.trim();
            const enContenedor = document.getElementById('en_contenedor').value;
            const trabajador   = document.getElementById('trabajador').value;
            
            // Check if at least one filter is selected
            if (!contenedor && !activoTexto && !enContenedor && !trabajador) {
                // Prevent form submission
                event.preventDefault();
                
                // Show SweetAlert message
                showSweetAlertError('Atención', 'Debe escoger algún filtro')
                return false;
            }
            
            // Allow form submission if at least one filter is selected
            return true;
        });
    }
    
    // --- Handle Image Viewing ---
    const tableBody          = document.getElementById('inventario-table-body');
    const imagenesModal      = document.getElementById('imagenes-modal');
    const imagenesModalLabel = document.getElementById('imagenes-modal-label');
    const imagenesContainer  = document.getElementById('imagenes-container');
    const noImagenesMessage  = document.getElementById('no-imagenes-message');
    const loadingImagenes    = document.getElementById('loading-imagenes');
    
    // Initialize the modals
    const modal = new bootstrap.Modal(imagenesModal);
    
    // --- Handle Move Asset Modal ---
    const moverActivoModal = document.getElementById('mover-activo-modal');
    const moverActivoModalInstance = new bootstrap.Modal(moverActivoModal);
    const moverActivoForm = document.getElementById('mover-activo-form');
    const btnConfirmarMover = document.getElementById('btn-confirmar-mover');
    const moverActivoError = document.getElementById('mover-activo-error');
    
    if (tableBody) {
        tableBody.addEventListener('click', function (event) {
            const verImagenesButton = event.target.closest('.btn-ver-imagenes');
            
            // --- Handle Ver Imagenes Click ---
            if (verImagenesButton) {
                event.preventDefault();
                const activoId = verImagenesButton.dataset.activoid;
                const descripcion = verImagenesButton.dataset.descripcion || 'Activo';
                
                // Update modal title
                imagenesModalLabel.textContent = `Imágenes del Activo: ${descripcion}`;
                
                // Clear previous images
                imagenesContainer.innerHTML = '';
                
                // Show loading indicator
                loadingImagenes.classList.remove('d-none');
                noImagenesMessage.classList.add('d-none');
                
                // Show the modal
                modal.show();
                
                // Fetch images for this activo
                fetch(`get_activo_imagenes?id_activo=${activoId}`)
                    .then(response => response.json())
                    .then(data => {
                        // Hide loading indicator
                        loadingImagenes.classList.add('d-none');
                        
                        if (data.length === 0) {
                            // Show no images message
                            noImagenesMessage.classList.remove('d-none');
                        } else {
                            // Display the images
                            data.forEach(imagen => {
                                const imgContainer = document.createElement('div');
                                imgContainer.className = 'image-item';
                                
                                const img = document.createElement('img');
                                img.src = `resources/uploads/activos/${imagen.nombre_archivo}`;
                                img.alt = `Imagen ${imagen.id}`;
                                img.className = 'img-thumbnail';
                                img.style.maxWidth = '200px';
                                img.style.height = 'auto';
                                
                                imgContainer.appendChild(img);
                                imagenesContainer.appendChild(imgContainer);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching images:', error);
                        loadingImagenes.classList.add('d-none');
                        imagenesContainer.innerHTML = '<div class="alert alert-danger">Error al cargar las imágenes</div>';
                    });
            }
            
            // --- Handle Mover Activo Click ---
            const moverActivoButton = event.target.closest('.btn-mover-activo');
            if (moverActivoButton) {
                event.preventDefault();
                
                // Get data from button attributes
                const inventarioId = moverActivoButton.dataset.inventarioId;
                const activoDescripcion = moverActivoButton.dataset.activoDescripcion;
                const activoMarca = moverActivoButton.dataset.activoMarca;
                const activoModelo = moverActivoButton.dataset.activoModelo;
                const activoSerie = moverActivoButton.dataset.activoSerie;
                const contenedorActualId = moverActivoButton.dataset.contenedorActualId;
                const contenedorActualDescripcion = moverActivoButton.dataset.contenedorActualDescripcion;
                
                // Populate modal with asset information
                document.getElementById('modal-activo-descripcion').textContent = activoDescripcion;
                document.getElementById('modal-activo-marca').textContent = activoMarca;
                document.getElementById('modal-activo-modelo').textContent = activoModelo;
                document.getElementById('modal-activo-serie').textContent = activoSerie;
                document.getElementById('modal-contenedor-actual').textContent = contenedorActualDescripcion;
                document.getElementById('inventario-id').value = inventarioId;
                
                // Reset form and hide error
                moverActivoForm.reset();
                document.getElementById('inventario-id').value = inventarioId;
                moverActivoError.classList.add('d-none');
                
                // Remove current container from destination options
                const destinoSelect = document.getElementById('destino-contenedor');
                Array.from(destinoSelect.options).forEach(option => {
                    if (option.value === contenedorActualId) {
                        option.disabled = true;
                        option.textContent = option.textContent + ' (Contenedor actual)';
                    } else {
                        option.disabled = false;
                        option.textContent = option.textContent.replace(' (Contenedor actual)', '');
                    }
                });
                
                // Show the modal
                moverActivoModalInstance.show();
            }
        });
    }
    
    // --- Handle Move Asset Form Submission ---
    if (btnConfirmarMover) {
        btnConfirmarMover.addEventListener('click', function(event) {
            event.preventDefault();
            
            // Validate form
            const destinoContenedor = document.getElementById('destino-contenedor').value;
            const inventarioId = document.getElementById('inventario-id').value;
            
            if (!destinoContenedor) {
                moverActivoError.textContent = 'Por favor seleccione un contenedor destino.';
                moverActivoError.classList.remove('d-none');
                return;
            }
            
            // Hide error and disable button
            moverActivoError.classList.add('d-none');
            btnConfirmarMover.disabled = true;
            btnConfirmarMover.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span> Moviendo...';
            
            // Prepare form data
            const formData = new FormData();
            formData.append('inventario_id', inventarioId);
            formData.append('destino_contenedor_id', destinoContenedor);
            
            // Submit via AJAX
            fetch('mover_activo', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Success - redirect to refresh the page
                        window.location.href = data.redirect || 'linventario';
                    } else {
                        // Show error message
                        moverActivoError.textContent = data.message || 'Error al mover el activo';
                        moverActivoError.classList.remove('d-none');
                        
                        // Re-enable button
                        btnConfirmarMover.disabled = false;
                        btnConfirmarMover.innerHTML = '<i class="fa fa-exchange-alt fa-fw me-1"></i> Mover Activo';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    moverActivoError.textContent = 'Error de conexión. Por favor intente nuevamente.';
                    moverActivoError.classList.remove('d-none');
                    
                    // Re-enable button
                    btnConfirmarMover.disabled = false;
                    btnConfirmarMover.innerHTML = '<i class="fa fa-exchange-alt fa-fw me-1"></i> Mover Activo';
                });
        });
    }
})