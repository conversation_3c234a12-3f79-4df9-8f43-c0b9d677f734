<?php

// Iniciar sesión si es necesario
use App\classes\Inventario;
use App\classes\InventarioMovimiento;
use App\classes\Contenedor;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

// Incluir archivos necesarios
require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
	http_response_code(405);
	echo json_encode(['success' => false, 'message' => 'Método no permitido']);
	exit;
}

// Verificar que sea una petición AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
	http_response_code(400);
	echo json_encode(['success' => false, 'message' => 'Petición inválida']);
	exit;
}

// Obtener y validar parámetros
$inventario_id         = filter_input(INPUT_POST, 'inventario_id', FILTER_VALIDATE_INT);
$destino_contenedor_id = filter_input(INPUT_POST, 'destino_contenedor_id', FILTER_VALIDATE_INT);

if (!$inventario_id || !$destino_contenedor_id) {
	echo json_encode(['success' => false, 'message' => 'Parámetros inválidos']);
	exit;
}

try {
	// Iniciar transacción
	$conexion->beginTransaction();
	
	// Obtener el registro de inventario actual
	$inventario_actual = Inventario::get($inventario_id, $conexion);
	
	if (!$inventario_actual) {
		throw new Exception("No se encontró el registro de inventario especificado");
	}
	
	// Verificar que el activo no esté prestado a un trabajador
	if ($inventario_actual->getEn_contenedor() != 1) {
		throw new Exception("No se puede mover el activo porque está prestado a un trabajador");
	}
	
	// Obtener información del contenedor actual y destino
	$contenedor_actual_id = $inventario_actual->getId_contenedor();
	$activo_id            = $inventario_actual->getId_activo();
	
	// Verificar que el contenedor destino existe y está activo
	$contenedor_destino = Contenedor::get($destino_contenedor_id, $conexion);
	if (!$contenedor_destino || !$contenedor_destino->isActivo()) {
		throw new Exception("El contenedor destino no existe o no está activo");
	}
	
	// Verificar que no sea el mismo contenedor
	if ($contenedor_actual_id == $destino_contenedor_id) {
		throw new Exception("El activo ya se encuentra en el contenedor seleccionado");
	}
	
	// Verificar que no exista ya un registro para este activo en el contenedor destino
	$inventario_existente = Inventario::get_by_contenedor_activo($destino_contenedor_id, $activo_id, $conexion);
	if ($inventario_existente) {
		throw new Exception("El activo ya existe en el contenedor destino");
	}
	
	// PASO 1: Eliminar del contenedor actual
	$eliminacion_exitosa = Inventario::eliminar($inventario_id, $conexion);
	
	if (!$eliminacion_exitosa) {
		throw new Exception("Error al remover el activo del contenedor actual");
	}
	
	// PASO 2: Crear registro de movimiento de salida
	$movimiento_salida = new InventarioMovimiento();
	$movimiento_salida->setId_contenedor($contenedor_actual_id);
	$movimiento_salida->setId_activo($activo_id);
	$movimiento_salida->setCantidad(1);
	$movimiento_salida->setId_usuario($_SESSION[USR_SESSION]);
	$movimiento_salida->setTipo_movimiento("salida");
	
	$movimiento_salida_id = $movimiento_salida->crear($conexion);
	
	if (!$movimiento_salida_id) {
		throw new Exception("Error al registrar el movimiento de salida");
	}
	
	// PASO 3: Crear nuevo registro en el contenedor destino
	$nuevo_inventario = new Inventario();
	$nuevo_inventario->setId_contenedor($destino_contenedor_id);
	$nuevo_inventario->setId_activo($activo_id);
	$nuevo_inventario->setCantidad(1);
	$nuevo_inventario->setEn_contenedor(1);
	$nuevo_inventario->setId_trabajador(null);
	
	$nuevo_inventario_id = $nuevo_inventario->crear($conexion);
	
	if (!$nuevo_inventario_id) {
		throw new Exception("Error al agregar el activo al contenedor destino");
	}
	
	// PASO 4: Crear registro de movimiento de entrada
	$movimiento_entrada = new InventarioMovimiento();
	$movimiento_entrada->setId_contenedor($destino_contenedor_id);
	$movimiento_entrada->setId_activo($activo_id);
	$movimiento_entrada->setCantidad(1);
	$movimiento_entrada->setId_usuario($_SESSION[USR_SESSION]);
	$movimiento_entrada->setTipo_movimiento("entrada");
	
	$movimiento_entrada_id = $movimiento_entrada->crear($conexion);
	
	if (!$movimiento_entrada_id) {
		throw new Exception("Error al registrar el movimiento de entrada");
	}
	
	// Confirmar transacción
	$conexion->commit();
	
	// Establecer mensaje de éxito en sesión
	$_SESSION['flash_message_success'] = "El activo se movió correctamente al nuevo contenedor.";
	
	// Respuesta exitosa
	echo json_encode([
		                 'success'  => true,
		                 'message'  => 'Activo movido correctamente',
		                 'redirect' => 'linventario'
	                 ]);
	
} catch (Exception $e) {
	// Revertir transacción en caso de error
	if ($conexion->inTransaction()) {
		$conexion->rollBack();
	}
	
	// Log del error
	error_log("Error al mover activo: " . $e->getMessage());
	
	// Respuesta de error
	echo json_encode([
		                 'success' => false,
		                 'message' => $e->getMessage()
	                 ]);
}

?>
