RewriteEngine On

# Redirect to HTTPS and non-www, but NOT on localhost
RewriteCond %{HTTP_HOST} !^localhost$ [NC]
RewriteCond %{HTTP_HOST} ^www\.serimpro\.com\.co$ [NC]
RewriteRule ^(.*)$ https://serimpro.com.co/$1 [L,R=301]

RewriteCond %{HTTP_HOST} ^serimpro\.com\.co$ [NC]
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://serimpro.com.co/$1 [L,R=301]

RewriteRule ^index/?$ index.php
RewriteRule ^landing/?$ src/landing.php

RewriteRule ^login/?$ src/login.php
RewriteRule ^dashboard/?$ src/dashboard.php
RewriteRule ^lusuarios/?$ src/lusuarios.php
RewriteRule ^iusuario/?$ src/iusuario.php
RewriteRule ^lcontenedores/?$ src/lcontenedores.php
RewriteRule ^icontenedor/?$ src/icontenedor.php
RewriteRule ^cambiar_clave/?$ src/cambiar_clave.php
RewriteRule ^lactivos/?$ src/lactivos.php
RewriteRule ^eactivo/?$ src/eactivo.php
RewriteRule ^iactivo/?$ src/iactivo.php
RewriteRule ^linventario/?$ src/linventario.php
RewriteRule ^iinventario/?$ src/iinventario.php
RewriteRule ^sinventario/?$ src/sinventario.php
RewriteRule ^qractivo/?$ src/qractivo.php
RewriteRule ^scanactivo/?$ src/scanactivo.php
RewriteRule ^linventario_movimientos/?$ src/linventario_movimientos.php
RewriteRule ^get_activo_imagenes/?$ src/get_activo_imagenes.php
RewriteRule ^get_activos_by_contenedor/?$ src/get_activos_by_contenedor.php
RewriteRule ^ltrabajadores/?$ src/ltrabajadores.php
RewriteRule ^itrabajador/?$ src/itrabajador.php
RewriteRule ^etrabajador/?$ src/etrabajador.php
RewriteRule ^prestar_activos/?$ src/prestar_activos.php
RewriteRule ^regresar_activos/?$ src/regresar_activos.php
RewriteRule ^export_inventario/?$ src/export_inventario.php
RewriteRule ^mover_activo/?$ src/mover_activo.php

RewriteRule ^cerrar/?$ src/sessions/cerrar.php
